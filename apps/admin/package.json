{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev-admin": "next dev", "dev-turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@repo/component-summary": "workspace:*", "@tanstack/react-query": "^5.75.0", "@tanstack/react-table": "^8.21.3", "@uniformdev/canvas-next": "^20.16.0", "axios": "^1.9.0", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "konva": "^9.3.20", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-intersection-observer": "^9.16.0", "react-konva": "^19.0.3", "react-phone-number-input": "^3.4.12", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "use-image": "^1.1.1", "uuid": "^11.1.0", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}