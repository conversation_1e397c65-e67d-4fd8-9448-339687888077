import {
  Controller,
  Post,
  Param,
  Body,
  Res,
  BadRequestException,
  Get,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { Response } from 'express';
import { ExportService } from './export.service';
import { ComponentSummaryService } from './component-summary.service';
import { ExportComponentsDto } from './dto/export.dto';
import { Public } from 'src/auth/decorators/public.decorator';

@Controller('export')
export class ExportController {
  constructor(
    private readonly exportService: ExportService,
    private readonly componentSummaryService: ComponentSummaryService,
  ) {}

  @Post('takeoffs/:takeoffId/excel')
  async exportTakeoffToExcel(
    @Param('takeoffId', ParseIntPipe) takeoffId: number,
    @Body() exportDto: ExportComponentsDto,
    @Res() res: Response,
  ): Promise<void> {
    if (
      exportDto.measurementView === 'current-page' &&
      !exportDto.blueprintImageId
    ) {
      throw new BadRequestException(
        'blueprintImageId is required for current-page export',
      );
    }

    await this.exportService.exportToExcel(
      takeoffId,
      exportDto.measurementView,
      exportDto.blueprintImageId,
      res,
    );
  }

  @Get('takeoffs/:takeoffId/summary')
  async getTakeoffSummary(
    @Param('takeoffId', ParseIntPipe) takeoffId: number,
    @Body() exportDto: ExportComponentsDto,
  ) {
    return this.componentSummaryService.generateExportData(
      takeoffId,
      exportDto.measurementView,
      exportDto.blueprintImageId,
    );
  }
}
