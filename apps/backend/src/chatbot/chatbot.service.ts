import { Injectable } from '@nestjs/common';
import { openai } from '@ai-sdk/openai';
import { streamText, generateObject } from 'ai';
import { z } from 'zod';
import { ChatRequestDto } from './dto/chat-message.dto';

@Injectable()
export class ChatbotService {
  async processChat(chatRequest: ChatRequestDto) {
    const { message, blueprintContext, drawingsContext, conversationHistory } =
      chatRequest;

    // Auto-detect context using AI
    const contextType = await this.detectContext(message);

    let systemPrompt = '';
    let userPrompt = message;
    let attachments: any[] = [];

    if (contextType === 'blueprint' && blueprintContext) {
      systemPrompt = this.getBlueprintSystemPrompt();
      userPrompt = this.formatBlueprintUserPrompt(message, blueprintContext);

      // Add image attachment for vision analysis
      attachments.push({
        name: blueprintContext.fileName,
        contentType: 'image/jpeg',
        url: blueprintContext.imageUrl,
      });
    } else if (contextType === 'drawings' && drawingsContext) {
      systemPrompt = this.getDrawingsSystemPrompt();
      userPrompt = this.formatDrawingsUserPrompt(message, drawingsContext);
    } else if (contextType === 'general') {
      systemPrompt = this.getGeneralSystemPrompt();
      userPrompt = message; // Use the original message for general questions
    }

    // Build conversation history for context
    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-5).map((msg) => ({
        role: msg.role,
        content: msg.content,
      })),
      {
        role: 'user',
        content: userPrompt,
        experimental_attachments:
          attachments.length > 0 ? attachments : undefined,
      },
    ];

    // Stream the response
    const result = streamText({
      model: openai('gpt-4o'),
      messages: messages as any,
      temperature: 0.7,
      maxTokens: 1000,
    });

    return result;
  }

  private async detectContext(
    message: string,
  ): Promise<'blueprint' | 'drawings' | 'general'> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({
          contextType: z
            .enum(['blueprint', 'drawings', 'general'])
            .describe(
              'The type of context based on the user message. Use "blueprint" for questions about architectural plans, building features, rooms, doors, windows, structural elements. Use "drawings" for questions about custom annotations, shapes, measurements, markups, or user-created drawings. Use "general" for basic questions, greetings, help requests, or general conversation that doesn\'t relate to blueprints or drawings.',
            ),
          reasoning: z
            .string()
            .describe('Brief explanation of why this context was chosen'),
        }),
        prompt: `Analyze this user message and determine if they are asking about:
1. "blueprint" - architectural plans, building features, rooms, doors, windows, structural elements, floor plans
2. "drawings" - custom annotations, shapes, measurements, markups, user-created drawings, circles, rectangles, lines
3. "general" - basic questions, greetings, help requests, general conversation, or questions not related to blueprints/drawings

User message: "${message}"

Consider the intent and keywords to classify appropriately. If the message is a greeting, general question, or doesn't relate to architectural content, classify as "general".`,
      });

      return object.contextType;
    } catch (error) {
      console.error('Context detection error:', error);
      // Default to general if detection fails
      return 'general';
    }
  }

  private getBlueprintSystemPrompt(): string {
    return `You are an AI assistant specialized in analyzing architectural blueprint drawings and plans. 
Your role is to help architects, engineers, and construction professionals understand and analyze building plans.

You can analyze:
- Doors and windows (count, types, locations)
- Room layouts and dimensions
- Structural elements
- Architectural features
- Building components
- Spatial relationships

When analyzing blueprints:
1. Be specific and accurate in your observations
2. Use architectural terminology appropriately
3. Provide counts when asked (e.g., "I can see 5 doors in this plan")
4. Describe locations clearly (e.g., "on the north wall", "in the main entrance")
5. If something is unclear in the image, mention the limitation

Keep responses concise but informative. Focus on what you can clearly observe in the blueprint image.`;
  }

  private getDrawingsSystemPrompt(): string {
    return `You are an AI assistant helping users understand their custom drawings and annotations on architectural blueprints.

You can analyze:
- Drawing shapes (rectangles, circles, lines, points, arrows)
- Annotations and comments
- Measurements and dimensions
- Drawing relationships and patterns
- Markup details

The drawings data includes:
- Shape types and configurations
- Coordinates and dimensions
- Colors and styling
- Associated component information
- Creation timestamps

When analyzing drawings:
1. Describe what types of shapes/annotations are present
2. Provide counts and summaries
3. Explain spatial relationships between drawings
4. Interpret measurement data when available
5. Help users understand their markup patterns

Keep responses helpful and focused on the user's custom annotations and drawings.`;
  }

  private getGeneralSystemPrompt(): string {
    return `You are a helpful AI assistant for a construction and architectural planning platform. You can help users with:

- General questions about construction and architecture
- Platform usage and features
- Basic greetings and conversation
- General knowledge questions
- Help and support requests

You are friendly, professional, and knowledgeable about construction and architectural topics. Keep responses concise and helpful. If users ask about specific blueprint analysis or drawing features, suggest they upload a blueprint or create drawings to get more detailed assistance.`;
  }

  private formatBlueprintUserPrompt(message: string, context: any): string {
    return `I'm analyzing a blueprint file named "${context.fileName}".

User question: ${message}

Please analyze the blueprint image and provide a helpful response based on what you can observe in the architectural drawing.`;
  }

  private formatDrawingsUserPrompt(message: string, context: any): string {
    const drawingsCount = context.drawings.length;
    const drawingTypes = this.analyzeDrawingTypes(context.drawings);

    return `I have ${drawingsCount} custom drawings/annotations on this blueprint:

Drawing summary:
${drawingTypes}

User question: ${message}

Please help me understand my drawings and annotations based on the data provided.`;
  }

  private analyzeDrawingTypes(drawings: any[]): string {
    const typeCounts: Record<string, number> = {};

    drawings.forEach((drawing) => {
      const config = drawing.config || {};
      const type = config.type || 'unknown';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    return Object.entries(typeCounts)
      .map(([type, count]) => `- ${count} ${type}${count > 1 ? 's' : ''}`)
      .join('\n');
  }
}
