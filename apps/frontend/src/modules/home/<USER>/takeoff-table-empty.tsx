'use client';

import { But<PERSON> } from '@/components/ui/button';
import { FileText, Plus } from 'lucide-react';

export function TakeoffTableEmpty() {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 mb-4">
        <FileText className="h-8 w-8 text-gray-500" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-1">
        No takeoffs yet
      </h3>
      <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
        Create your first takeoff to get started with QuickPlansAI
      </p>
      <Button
        id="empty-state-create-takeoff-button"
        onClick={() => {
          // Find the "New Takeoff" button by ID and click it
          const newTakeoffButton = document.getElementById(
            'create-takeoff-trigger',
          );
          if (newTakeoffButton) {
            (newTakeoffButton as HTMLButtonElement).click();
          }
        }}
      >
        <Plus className="mr-2 h-4 w-4" />
        Create Takeoff
      </Button>
    </div>
  );
}
