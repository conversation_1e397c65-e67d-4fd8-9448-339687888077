import { useCallback } from 'react';
import { useChat } from '@ai-sdk/react';
import { useTakeoffStore } from '../../../store/takeoff-store';
import { useGetDrawings } from '../../../api/queries';
import {
  ChatMessage,
  BlueprintContext,
  DrawingsContext,
} from '../types/chatbot-types';
import { v4 as uuidv4 } from 'uuid';

export const useChatbot = (takeoffId: string) => {
  const {
    selectedImage,
    selectedFile,
    chatbot,
    addChatMessage,
    setChatbotLoading,
    setChatbotError,
  } = useTakeoffStore();

  // Get drawings for current image
  const { data: drawingsData } = useGetDrawings(
    { blueprintImageId: selectedImage?.id || '' },
    { enabled: !!selectedImage?.id },
  );

  const { append } = useChat({
    api: '/api/v1/chatbot/chat',
    onResponse: async (response) => {
      if (!response.ok) {
        setChatbotError('Failed to get response from AI assistant');
      }
    },
    onFinish: (message) => {
      // Add AI response to store
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: message.content,
        timestamp: new Date(),
      };
      addChatMessage(aiMessage);
      setChatbotError(null);
      setChatbotLoading(false);
    },
    onError: (error) => {
      setChatbotLoading(false);
      setChatbotError(error.message || 'Something went wrong');
    },
  });

  const sendMessage = useCallback(
    async (message: string) => {
      if (!selectedImage || !selectedFile) {
        setChatbotError('Please select a blueprint image first');
        return;
      }

      setChatbotLoading(true);
      setChatbotError(null);

      // Add user message to store
      const userMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content: message,
        timestamp: new Date(),
      };
      addChatMessage(userMessage);

      // Prepare context data - send both contexts and let backend decide
      const blueprintContext: BlueprintContext = {
        imageUrl: selectedImage.path,
        fileName: selectedFile.fileName,
        takeoffId: takeoffId,
        blueprintFileId: selectedFile.id,
        blueprintImageId: selectedImage.id,
      };

      const drawingsContext: DrawingsContext = {
        drawings: drawingsData || [],
        imageUrl: selectedImage.path,
        takeoffId: takeoffId,
        blueprintImageId: selectedImage.id,
      };

      try {
        // Use the append function to send the message with context
        await append({
          role: 'user',
          content: message,
          data: JSON.parse(
            JSON.stringify({
              message,
              blueprintContext,
              drawingsContext,
              conversationHistory: chatbot.messages.slice(-10),
            }),
          ),
        });
      } catch (err) {
        setChatbotLoading(false);
        setChatbotError('Failed to send message');
        console.error('Chat error:', err);
      }
    },
    [
      selectedImage,
      selectedFile,
      takeoffId,
      drawingsData,
      chatbot.messages,
      addChatMessage,
      setChatbotLoading,
      setChatbotError,
      append,
    ],
  );

  return {
    sendMessage,
    isLoading: chatbot.isLoading,
    error: chatbot.error,
    messages: chatbot.messages,
  };
};
