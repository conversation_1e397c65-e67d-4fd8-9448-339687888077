import { TooltipProvider } from '@/components/ui/tooltip';
import {
  ArrowUpFromDot,
  Circle as CircleIcon,
  Hand,
  MessageCircle,
  MousePointer,
  Pen,
  Redo,
  Spline,
  Square,
  Squircle,
  Undo,
  Waypoints,
} from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { getMeasureToolProperties } from '../constants/measure-tools';
import { useUndoRedo } from '../hooks/useUndoRedo';
import { MeasureToolVariant, useTakeoffStore } from '../store/takeoff-store';
import { SelectedTool } from '../types/drawing-types';
import { getVisibleTools } from '../utils/tool-visibility';
import { MeasureToolDropdown } from './MeasureToolDropdown';
import { ToolbarButton } from './ToolbarButton';

interface DrawingToolbarProps {
  selectedTool: SelectedTool;
  onToolChange: (value: SelectedTool) => void;
}

/**
 * Figma-style bottom toolbar with icon-only buttons and hover tooltips for drawing tool selection and undo/redo functionality
 */
export function DrawingToolbar({
  selectedTool,
  onToolChange,
}: DrawingToolbarProps) {
  const { canUndo, canRedo, undo, redo } = useUndoRedo();
  const {
    isEditMode,
    selectedComponentItem,
    selectedMeasureTool,
    setSelectedMeasureTool,
    isMeasureMode,
    setIsMeasureMode,
  } = useTakeoffStore();

  // Determine if in measure mode
  useEffect(() => {
    setIsMeasureMode(!selectedComponentItem);
  }, [selectedComponentItem, setIsMeasureMode]);

  const visibleTools = getVisibleTools(selectedComponentItem);
  const isToolVisible = useCallback(
    (tool: SelectedTool) => visibleTools.includes(tool),
    [visibleTools],
  );

  // Auto-switch tool if current tool becomes unavailable
  useEffect(() => {
    if (
      !isMeasureMode &&
      !isToolVisible(selectedTool) &&
      selectedTool !== 'pan' &&
      selectedTool !== 'select'
    ) {
      onToolChange('pan');
    }
  }, [
    selectedComponentItem,
    selectedTool,
    onToolChange,
    isToolVisible,
    isMeasureMode,
  ]);

  const handleMeasureToolChange = (tool: MeasureToolVariant) => {
    setSelectedMeasureTool(tool);
    const toolProperties = getMeasureToolProperties(tool);
    onToolChange(toolProperties.baseTool as SelectedTool);
  };

  return (
    <TooltipProvider>
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-10 flex h-12 items-center gap-3 bg-background border border-border rounded-xl px-3 shadow-lg">
        {/* Undo/Redo buttons */}
        <div className="flex gap-1">
          <ToolbarButton
            icon={<Undo className="h-5 w-5" />}
            tooltip="Undo (Ctrl+Z)"
            onClick={undo}
            disabled={!canUndo || !isEditMode}
            aria-label="Undo"
          />
          <ToolbarButton
            icon={<Redo className="h-5 w-5" />}
            tooltip="Redo (Ctrl+Y)"
            onClick={redo}
            disabled={!canRedo || !isEditMode}
            aria-label="Redo"
          />
        </div>

        {/* Pan and Select tools - always visible */}
        <div className="flex gap-1">
          <ToolbarButton
            icon={<Hand className="h-5 w-5" />}
            tooltip="Pan (P)"
            onClick={() => onToolChange('pan')}
            isSelected={selectedTool === 'pan'}
            aria-label="Pan tool"
          />
          <ToolbarButton
            icon={<MousePointer className="h-5 w-5" />}
            tooltip={isEditMode ? 'Select (S)' : 'Select (Edit mode required)'}
            onClick={() => isEditMode && onToolChange('select')}
            isSelected={selectedTool === 'select'}
            disabled={!isEditMode}
            aria-label="Select tool"
          />
        </div>

        {/* Drawing tools dropdown */}
        {isEditMode && !selectedComponentItem && (
          <>
            <MeasureToolDropdown
              selectedMeasureTool={selectedMeasureTool}
              onMeasureToolChange={handleMeasureToolChange}
              selectedTool={selectedTool}
              disabled={!isEditMode}
            />

            {/* Comment tool - only visible when no component is selected */}
            <div className="flex gap-1">
              <ToolbarButton
                icon={<MessageCircle className="h-5 w-5" />}
                tooltip="Comment (M)"
                onClick={() => isEditMode && onToolChange('comment')}
                isSelected={selectedTool === 'comment'}
                disabled={!isEditMode}
                aria-label="Add comment"
              />
              <ToolbarButton
                icon={<ArrowUpFromDot className="h-5 w-5" />}
                tooltip="Arrow (A)"
                onClick={() => isEditMode && onToolChange('arrow')}
                isSelected={selectedTool === 'arrow'}
                disabled={!isEditMode}
                aria-label="Draw arrow"
              />
            </div>
          </>
        )}

        {/* Component-specific drawing tools */}
        {selectedComponentItem && (
          <div className="flex gap-1">
            {isToolVisible('rectangle') && (
              <ToolbarButton
                icon={<Square className="h-5 w-5" />}
                tooltip={
                  isEditMode
                    ? 'Rectangle (R)'
                    : 'Rectangle (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('rectangle')}
                isSelected={selectedTool === 'rectangle'}
                disabled={!isEditMode}
                aria-label="Draw rectangle"
              />
            )}
            {isToolVisible('circle') && (
              <ToolbarButton
                icon={<CircleIcon className="h-5 w-5" />}
                tooltip={
                  isEditMode ? 'Circle (C)' : 'Circle (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('circle')}
                isSelected={selectedTool === 'circle'}
                disabled={!isEditMode}
                aria-label="Draw circle"
              />
            )}
            {isToolVisible('ellipse') && (
              <ToolbarButton
                icon={<Squircle className="h-5 w-5" />}
                tooltip={
                  isEditMode ? 'Ellipse (E)' : 'Ellipse (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('ellipse')}
                isSelected={selectedTool === 'ellipse'}
                disabled={!isEditMode}
                aria-label="Draw ellipse"
              />
            )}
            {isToolVisible('freehand') && (
              <ToolbarButton
                icon={<Pen className="h-5 w-5" />}
                tooltip={
                  isEditMode ? 'Freehand (F)' : 'Freehand (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('freehand')}
                isSelected={selectedTool === 'freehand'}
                disabled={!isEditMode}
                aria-label="Draw freehand"
              />
            )}
            {isToolVisible('curve') && (
              <ToolbarButton
                icon={<Spline className="h-5 w-5" />}
                tooltip={
                  isEditMode ? 'Curve (V)' : 'Curve (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('curve')}
                isSelected={selectedTool === 'curve'}
                disabled={!isEditMode}
                aria-label="Draw curve"
              />
            )}
            {isToolVisible('point-to-point') && (
              <ToolbarButton
                icon={<Waypoints className="h-5 w-5" />}
                tooltip={
                  isEditMode
                    ? 'Point-to-Point (T)'
                    : 'Point-to-Point (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('point-to-point')}
                isSelected={selectedTool === 'point-to-point'}
                disabled={!isEditMode}
                aria-label="Draw point-to-point"
              />
            )}
            {isToolVisible('point') && (
              <ToolbarButton
                icon={<Waypoints className="h-5 w-5" />}
                tooltip={
                  isEditMode ? 'Point (Shift+P)' : 'Point (Edit mode required)'
                }
                onClick={() => isEditMode && onToolChange('point')}
                isSelected={selectedTool === 'point'}
                disabled={!isEditMode}
                aria-label="Draw point"
              />
            )}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
