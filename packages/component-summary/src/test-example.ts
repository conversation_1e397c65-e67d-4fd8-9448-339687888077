// Test example to verify the polygon area fix
import {
  getFreehandSurfaceMeasurements,
  getPointToPointSurfaceMeasurements,
  getRectangleMeasurements,
} from './utils/distance-utils';

// Test data matching the user's scenario
const scale = {
  num_metric: 1,
  num_unit: 'inch' as const,
  den_metric: 1,
  den_unit: 'ft' as const,
};

const paperSize = {
  width: 11,
  height: 8.5,
  unit: 'inch' as const,
};

const canvasSize = { width: 1100, height: 850 };

// Test rectangle (should give ~8.4 ft)
const rectangleWidth = 100; // pixels
const rectangleHeight = 100; // pixels

console.log('=== TESTING MEASUREMENT ACCURACY FIX ===');

const rectangleMeasurements = getRectangleMeasurements(
  rectangleWidth,
  rectangleHeight,
  scale,
  paperSize,
  canvasSize,
);

console.log('Rectangle Area:', rectangleMeasurements.area.formatted);

// Test polygon with same approximate area
const squarePolygonPoints = [
  0,
  0, // top-left
  100,
  0, // top-right
  100,
  100, // bottom-right
  0,
  100, // bottom-left
];

const polygonMeasurements = getFreehandSurfaceMeasurements(
  squarePolygonPoints,
  scale,
  paperSize,
  canvasSize,
);

console.log('Polygon Area (Freehand):', polygonMeasurements.area.formatted);

const pointToPointMeasurements = getPointToPointSurfaceMeasurements(
  squarePolygonPoints,
  [],
  scale,
  paperSize,
  canvasSize,
);

console.log(
  'Polygon Area (Point-to-Point):',
  pointToPointMeasurements.area.formatted,
);

console.log('=== RESULTS COMPARISON ===');
console.log(
  'Rectangle:',
  rectangleMeasurements.area.realWorld.toFixed(2),
  'sq ft',
);
console.log(
  'Freehand Polygon:',
  polygonMeasurements.area.realWorld.toFixed(2),
  'sq ft',
);
console.log(
  'Point-to-Point Polygon:',
  pointToPointMeasurements.area.realWorld.toFixed(2),
  'sq ft',
);

const difference1 = Math.abs(
  rectangleMeasurements.area.realWorld - polygonMeasurements.area.realWorld,
);
const difference2 = Math.abs(
  rectangleMeasurements.area.realWorld -
    pointToPointMeasurements.area.realWorld,
);

console.log(
  'Difference (Rectangle vs Freehand):',
  difference1.toFixed(4),
  'sq ft',
);
console.log(
  'Difference (Rectangle vs Point-to-Point):',
  difference2.toFixed(4),
  'sq ft',
);

if (difference1 < 0.01 && difference2 < 0.01) {
  console.log(
    '✅ SUCCESS: All measurements match within 0.01 sq ft tolerance!',
  );
} else {
  console.log('❌ ISSUE: Measurements still differ significantly');
}
